//MODULE
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

//SERVICES
import { CommonFunctionsService } from 'src/app/utils/common-functions/common-functions.service';
import { MessageConstant } from 'src/app/utils/message-constant';
import { CreditService } from 'src/app/providers/credit/credit.service';
import { BuyersService } from 'src/app/providers/buyers/buyers.service';

// COMPONENTS
import { ConfirmationDialogComponent } from '../dialog/confirmation-dialog/confirmation-dialog.component';
import { SendBulkEmailDialogComponent } from '../dialog/send-bulk-email-dialog/send-bulk-email-dialog.component';
import { SendBulkSmsDialogComponent } from '../dialog/send-bulk-sms-dialog/send-bulk-sms-dialog.component';

//UTILS
import { StatusConstant } from 'src/app/utils/status-constant';
import { SharedService } from '../shared.service';
import { ResponseModel } from 'src/app/utils/models/response';
import { MatDialog } from '@angular/material/dialog';
import { ErrorModel } from 'src/app/utils/models/error';
import { VendorsService } from 'src/app/providers/vendors/vendors.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ExplainersPopUpService } from 'src/app/utils/explainers/explainer-popup.service';
import { MiscellaneousConstant } from 'src/app/utils/miscellaneous-constant';

@Component({
  selector: 'app-filter-bar',
  templateUrl: './filter-bar.component.html',
  styleUrls: ['./filter-bar.component.scss'],
  standalone: false,
})
export class FilterBarComponent implements OnInit, OnChanges {
  subject: Subject<any> = new Subject();

  @HostListener('window:click', ['$event.target'])
  checkActionModal($event) {
    const selectField = document.getElementById('re-select-field');
    if (selectField) {
      const isClickInside = selectField.contains(<HTMLInputElement>$event);

      if (!isClickInside) {
        this.isMenuVisible = false;
      }
    }

    const selectFieldOne = document.getElementById('re-select-field-one');
    if (selectFieldOne) {
      const isClickInsideOne = selectFieldOne.contains(
        <HTMLInputElement>$event
      );

      if (!isClickInsideOne) {
        this.isMenuVisibleOne = false;
      }
    }

    const columnCustomizer = document.querySelector('.column-customizer');
    const columnsButton = document.querySelector('.re-addlead-btn');

    if (columnCustomizer && this.showColumnPanel) {
      const isClickOnColumnsButton =
        columnsButton && columnsButton.contains(<HTMLElement>$event);

      const isClickInsidePanel = columnCustomizer.contains(<HTMLElement>$event);
      const cancelButton = columnCustomizer.querySelector('.cancel-btn');
      const isClickOnCancelButton =
        cancelButton && cancelButton.contains(<HTMLElement>$event);

      // Check if we're in the middle of a drag operation
      const isDragging = document.querySelector('.cdk-drag-dragging') !== null;
      const isDropListDragging =
        document.querySelector('.cdk-drop-list-dragging') !== null;

      // Only close the panel if we're not dragging and conditions are met
      if (
        ((!isClickInsidePanel && !isClickOnColumnsButton) ||
          isClickOnCancelButton) &&
        !isDragging &&
        !isDropListDragging
      ) {
        this.showColumnPanel = false;
      }
    }
  }

  @Input() moduleIcon: string;
  @Input() moduleName: string;
  @Input() linkTitle: string = '';
  @Input() linkRedirect: string = '';
  @Input() linkLeadId: string = '';
  @Input() linkAddress: string = '';
  @Input() isModuleClickExists: boolean = false;
  @Input() isAddModal: boolean = false;
  @Input() subModuleName: string;
  @Input() subModuleNameDetails: string;
  @Input() isModuleCountEnabled: boolean = false;
  @Input() moduleData: any;
  @Input() columnsButtonEnabled: boolean = false;
  @Input() isReduceGrid: boolean = false;
  @Input() isExportEnable: boolean = false;
  @Input() moduleId: string;
  @Input() totalRecord: number = 0;
  @Input() isWhiteBoardEnable: boolean = false;
  @Input() currentPage: number = 0;
  @Input() isActionBarEnabled: boolean = false;
  @Input() toggleActionBar: boolean = false;
  @Input() additionalActions: any[] = [];
  @Input() originalColumns: any[] = [];
  @Input() selectedItems: any[] = [];
  @Input() isbackToLeadEnable: boolean = false;
  @Input() isImportEnabled: boolean = false;
  @Input() filesInProgress: any[] = [];
  @Input() selectActionType: string = '';
  @Input() isFilter: boolean = false;
  @Input() filterObj: any = {};
  @Input() searchStr: string = '';
  @Input() latestCampaign: any = {};
  @Input() isSelectBarEnabled: boolean = false;
  @Input() currentModule: string;
  @Input() isRefreshEnabled: boolean = false;
  @Input() isSearchBarEnabled: boolean = false;
  @Input() assignFilterData: any;
  @Input() isSummaryGenrate: boolean = false;
  @Input() aiGenrateBtnText: any;

  @Output() _callBackFunction = new EventEmitter<any>();
  @Output() _emitReduce: EventEmitter<any> = new EventEmitter<any>();
  @Output() _exportData: EventEmitter<any> = new EventEmitter<any>();
  @Output() _resetFilter: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitRefresh: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitTheme: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitSelectBuyerAction: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitView: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitSort: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitWhiteBoardListGroup: EventEmitter<any> =
    new EventEmitter<any>();
  @Output()
  _showBuyerInboxMessages: EventEmitter<any> = new EventEmitter<any>();
  @Output() _showVendorInboxMessages: EventEmitter<any> =
    new EventEmitter<any>();
  @Output() _emitFilterSearch: EventEmitter<any> = new EventEmitter<any>();
  @Output() _resetSearch: EventEmitter<any> = new EventEmitter<any>();
  @Output() _emitColumnConfig = new EventEmitter<any>();

  // Individual dashboard genrate Summary
  @Output() _startGenrateSummary: EventEmitter<any> = new EventEmitter<any>();
  @Output() _showTheNormalView: EventEmitter<any> = new EventEmitter<any>();
  @Output() _addGlobalFolder: EventEmitter<any> = new EventEmitter<any>();

  statusConstant = StatusConstant;
  messageConstant = MessageConstant;
  dialogRef: any;
  isList: boolean = false;
  activeButton: string = 'all';
  selectItemsCount: number = 0;
  selectedValue: string = '';
  selectedTo: number = 1;
  selectedFrom: number = 10;
  isMenuVisibleOne: boolean = false;
  isMenuVisible: boolean = false;
  invalidInput: boolean = false;
  invalidInput1: boolean = false;
  isFilterBuyer: boolean = false;
  showColumnPanel: boolean = false;
  isFilterKpiGoal: boolean = false;
  showRoles: boolean = false;
  buyerFilter: any = {};
  vendorFilter: any = {};
  isSearchOpen: boolean = false;
  searchDataForm: FormGroup;
  isManual: boolean = false;
  view: any[] = [
    {
      name: 'LIST',
      toolName: 'List',
      icon: '/assets/images/list',
      isActive: false,
    },
    {
      name: 'GRID',
      toolName: 'Grid',
      icon: '/assets/images/grid',
      isActive: true,
    },
  ];
  sort: any[] = [];
  selectedListType: string = 'Show All';
  searchText: string = '';
  isDarkThemeApplied: any = false;
  columns: any[] = [];
  selectedRoles: any[] = [];
  columnsInitialized: boolean = false;
  roleOptions: any[] = [];

  constructor(
    private _router: Router,
    private route: ActivatedRoute,
    public _utilities: CommonFunctionsService,
    private _toastrService: ToastrService,
    private _loaderService: NgxUiLoaderService,
    private _sharedService: SharedService,
    private dialog: MatDialog,
    private _creditService: CreditService,
    public _commonFunctions: CommonFunctionsService,
    private _buyerService: BuyersService,
    private _vendorService: VendorsService,
    private formBuilder: FormBuilder,
    public _explainersPopUpService: ExplainersPopUpService,
    private cdr: ChangeDetectorRef
  ) {}

  // Add event listeners for drag operations
  private setupDragListeners(): void {
    // Add event listeners to detect drag operations
    document.addEventListener('dragstart', () => {
      document.body.classList.add('dragging-active');
    });

    document.addEventListener('dragend', () => {
      document.body.classList.remove('dragging-active');
    });

    // For CDK drag-drop
    document.addEventListener('mousedown', (e) => {
      if (
        e.target &&
        ((e.target as HTMLElement).classList.contains('drag-icon') ||
          (e.target as HTMLElement).closest('.cdk-drag-handle'))
      ) {
        document.body.classList.add('dragging-active');
      }
    });

    document.addEventListener('mouseup', () => {
      // Small delay to ensure the drop event completes
      setTimeout(() => {
        document.body.classList.remove('dragging-active');
      }, 100);
    });
  }

  ngOnInit(): void {
    // Setup drag listeners
    this.setupDragListeners();
    if (
      this.moduleId === StatusConstant.MainStatusId.LEAD ||
      this.moduleId === StatusConstant.MainStatusId.WHOLESALEPIPELINE
    ) {
      this.sort = [
        {
          name: 'DESC',
          tooltip:
            'Recently updated ' +
            this.moduleName.toLocaleLowerCase() +
            ' on top',
          icon: '/assets/images/asc',
          isActive: true,
        },
        {
          name: 'ASC',
          tooltip:
            'Recently updated ' +
            this.moduleName.toLocaleLowerCase() +
            ' at the bottom',
          icon: '/assets/images/desc',
          isActive: false,
        },
      ];
    }
    let hasLeadType;
    this.route.queryParams.subscribe((params) => {
      hasLeadType = params['leadType'] || null;
      if (this.moduleId == StatusConstant.MainStatusId.LEAD && hasLeadType) {
        this.isList = true;
      }
    });
    this.searchDataForm = this.formBuilder.group({
      searchText: [''],
    });
    this.isDarkThemeApplied =
      localStorage.getItem('isDarkModeEnable') === 'true';
    if (this.moduleName === 'Goal Tracker') {
      if (this.isDarkThemeApplied) {
        document.body.classList.add('dark-theme');
      } else {
        document.body.classList.remove('dark-theme');
      }
    } else if (this.moduleName === 'CEO Dashboard') {
      if (this.isDarkThemeApplied) {
        document.body.classList.add('dark-theme');
      } else {
        document.body.classList.remove('dark-theme');
      }
    } else if (this.moduleName === 'Individual Dashboard') {
      if (this.isDarkThemeApplied) {
        document.body.classList.add('dark-theme');
      } else {
        document.body.classList.remove('dark-theme');
      }
    } else {
      document.body.classList.remove('dark-theme');
    }

    if (localStorage.getItem('isManual') == 'true') {
      this.isManual = true;
    } else {
      this.isManual = false;
    }
  }

  ngOnChanges() {
    // Only reset columns if originalColumns has changed or columns is empty
    if (
      this.originalColumns &&
      (!this.columns.length ||
        JSON.stringify(this.originalColumns) !== JSON.stringify(this.columns))
    ) {
      // Deep clone to avoid reference issues
      this.columns = JSON.parse(JSON.stringify(this.originalColumns));

      if (!this.columnsInitialized) {
        this.ensureCorrectColumnOrder();
        this.columnsInitialized = true;
      }

      // Initialize roles for Team Assigned column
      const teamAssignedColumn = this.columns.find(
        (col) => col.label === 'Team Assigned'
      );
      if (teamAssignedColumn) {
        // Add hasRoles property if it doesn't exist
        if (!teamAssignedColumn.hasOwnProperty('hasRoles')) {
          teamAssignedColumn.hasRoles = true;
        }

        // Initialize roles array if it doesn't exist
        if (!teamAssignedColumn.hasOwnProperty('roles')) {
          teamAssignedColumn.roles = Object.entries(
            MiscellaneousConstant.roleNames
          ).map(([roleId, roleName]) => ({
            roleId,
            roleName,
            isVisible: true,
          }));
        }

        // Update selectedRoles based on visible roles
        this.selectedRoles = teamAssignedColumn.roles
          .filter((role) => role.isVisible)
          .map((role) => role.roleId);

        // Initialize roleOptions array for better performance
        this.roleOptions = teamAssignedColumn.roles.map((role) => ({
          key: role.roleId,
          label: role.roleName,
          isVisible: role.isVisible,
        }));
      }

      // Force change detection
      this.cdr.detectChanges();
    }
  }

  ensureCorrectColumnOrder() {
    const requiredColumn = 'Seller Name';

    // If Seller Name is not the first column, move it there
    if (this.columns.length > 0 && this.columns[0].label !== requiredColumn) {
      const sellerNameColumn = this.columns.find(
        (item) => item.label === requiredColumn
      );

      if (sellerNameColumn) {
        sellerNameColumn.isVisible = true;
        this.columns = [
          sellerNameColumn,
          ...this.columns.filter((column) => column.label !== requiredColumn),
        ];

        // Update sort property
        this.columns.forEach((col, index) => (col.sort = index));
      }
    }
  }
  // Genrate Individual dashboard Summary
  genrateAiSummary() {
    this._startGenrateSummary.emit(true);
  }

  backToNormalView() {
    this._showTheNormalView.emit(false);
  }

  onLinkAddress() {
    if (this.linkLeadId) {
      this._router.navigate([`/leads/details`], {
        queryParams: { leadsId: this.linkLeadId },
      });
    }
  }

  callAction() {
    this.goToAddModule();
  }

  goToAddModule() {
    if (typeof this.isAddModal !== 'undefined' && this.isAddModal == true) {
    } else {
      if (this.moduleName != 'Buyer/Agents' && this.moduleName != 'vendors') {
        this._router.navigate([`${this.moduleName.toLocaleLowerCase()}/add`]);
      }
    }

    if (this.moduleName === 'Buyer/Agents') {
      this._router.navigate(['add'], { relativeTo: this.route });
    }
    if (this.moduleName == 'vendors') {
      this._router.navigate([`vendors/add`]);
    }
  }
  toggleTheme(isDarkTheme) {
    this.isDarkThemeApplied = isDarkTheme;

    if (this.isDarkThemeApplied) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
    localStorage.setItem('isDarkModeEnable', isDarkTheme);
    this._emitTheme.emit(isDarkTheme);
  }

  ngOnDestroy(): void {
    document.body.classList.remove('dark-theme');
    // if (this.moduleName === 'Goal Tracker') {
    //   this.isDarkThemeApplied = !this.isDarkThemeApplied;

    //   if (this.isDarkThemeApplied) {
    //     document.body.classList.add('dark-theme');
    //   } else {
    //     document.body.classList.remove('dark-theme');
    //   }
    // } else if (this.moduleName === 'CEO Dashboard') {
    //   this.isDarkThemeApplied = !this.isDarkThemeApplied;

    //   if (this.isDarkThemeApplied) {
    //     document.body.classList.add('dark-theme');
    //   } else {
    //     document.body.classList.remove('dark-theme');
    //   }
    // } else {
    //   document.body.classList.remove('dark-theme');
    // }
  }

  getCount() {
    if (this.moduleData?.count) {
      return this.moduleData?.count || 0;
    } else if (typeof this.moduleData === 'object') {
      let count = 0;
      Object.keys(this.moduleData).forEach((data) => {
        count += this.moduleData[data]?.count || 0;
      });
      return count;
    }
  }

  setGridView(type) {
    if (type != this._utilities.reduceGrid) {
      this._emitReduce.emit(type);
    }
  }

  exportData() {
    this._exportData.emit();
  }

  resetFilter() {
    this.clearCustomSelection();
    this._resetFilter.emit();
  }

  clearCustomSelection(bool?) {
    this.selectedValue = null;
    this.selectItemsCount = 0;
    this.selectedTo = 1;
    this.selectedFrom = this.totalRecord;

    if (!bool) this._emitRefresh.emit(true);
  }

  toggleMenu() {
    if (
      this.moduleId == this.statusConstant.MainStatusId.LEAD ||
      this.moduleId == this.statusConstant.MainStatusId.WHOLESALEPIPELINE ||
      this.moduleId == this.statusConstant.MainStatusId.BUYER ||
      this.moduleId == this.statusConstant.MainStatusId.VENDOR
    ) {
      this.isMenuVisibleOne = !this.isMenuVisibleOne;
      return;
    }
    this.isMenuVisible = !this.isMenuVisible;
  }

  selectAction(mode) {
    switch (mode) {
      case 1:
        this.selectItemsCount = this.moduleData?.items?.length;
        this._emitSelectBuyerAction.emit({
          mode: 'Self',
          val: this.selectItemsCount,
          currentPage: this.currentPage,
        });
        break;
      case 2:
        this.selectItemsCount = this.totalRecord;
        this._emitSelectBuyerAction.emit({
          mode: 'All',
          val: this.totalRecord,
        });
        break;

        // CUSTOM ACTION

        // case 3:
        //   this._emitSelectBuyerAction.emit({
        //     mode: 'Custom',
        //     from: this.selectedTo,
        //     to: this.selectedFrom,
        //     currentPage: this.currentPage,
        //   });
        break;
    }
  }
  onSearchSubmit() {
    let searchVal = '';
    if (this.searchDataForm.value) {
      searchVal = this.searchDataForm.value.searchText;
    }
    this._emitFilterSearch.emit(searchVal);
  }
  onSearchCallLog() {
    this.subject.next(void 0);
  }

  expandSearch() {
    this.isSearchOpen = !this.isSearchOpen;
  }
  resetSearch() {
    this.isSearchOpen = false;
    this.searchDataForm.patchValue({
      searchText: '',
    });
    this._resetSearch.emit();
  }
  refresh() {
    this._emitRefresh.emit(true);
  }
  showMessages(name, type) {
    this.activeButton = type;
    if (name == 'Buyer/Agents') {
      this._showBuyerInboxMessages.emit(type);
    }
    if (name == 'Vendors') {
      this._showVendorInboxMessages.emit(type);
    }
  }

  bulkUpload() {
    if (this.filesInProgress.length) {
      this._toastrService.error(this.messageConstant.bulkImportLimitExceed);
      return;
    }

    if (this.moduleName === 'Vendors') {
      this._toastrService.error(this.messageConstant.comingSoon);
      return;
    } else this._router.navigate(['/buyers/bulk-upload']);
  }

  validateInput(event: any) {
    this.invalidInput = event.target.value < 0 ? true : false;
    this.invalidInput1 =
      parseInt(this.selectedTo.toString()) >
        parseInt(this.selectedFrom.toString()) && !this.invalidInput
        ? true
        : false;
  }

  async emitSelected(action) {
    if (action.event === 'BULK_EMAIL') {
      if (!this._utilities.currentPlanData?.isAllowBulkEmail) {
        this._loaderService.stop();
        this._toastrService.info(this.messageConstant.featureMessage, '', {
          enableHtml: true,
        });
        return;
      }
      this.openBulkEmailModal();
      return;
    }

    if (action.event === 'BULK_SMS') {
      if (!this._utilities.currentPlanData?.isAllowBulkSMS) {
        this._loaderService.stop();
        this._toastrService.info(this.messageConstant.featureMessage, '', {
          enableHtml: true,
        });
        return;
      }
      this.openBulkSMSModal();
      return;
    }
    this._emitSelected.emit({ ...action, selectedItems: this.selectedItems });
  }
  openBulkEmailModal() {
    let length = 0;
    if (this.selectActionType === 'All') {
      length = this.totalRecord;
    } else {
      length = this.selectedItems.length;
      const emailLength = this.selectedItems.filter((x) => x.email);
      if (!emailLength?.length) {
        this._toastrService.error(this.messageConstant.selectContactWithEmail);
        return;
      }
    }

    if (!length) {
      this._toastrService.error(this.messageConstant.selectContactWithEmail);
      return;
    }

    if (length > environment.buyers.bulkEmailLimit) {
      const limit = this.messageConstant.selectBulkContactWithLimit
        .replace('[[TYPE]]', 'EMAIL')
        .replace('[[LIMIT]]', `${environment.buyers.bulkEmailLimit}`);
      this._toastrService.error(limit);
      return;
    }

    const data = {
      page: 1,
      limit: 1000,
      moduleId: this.moduleId,
    };

    this._loaderService.start();
    this._sharedService.getEmailTemplates(data).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode === 200) {
          let text = `You have selected {{COUNT}} records to send Bulk Email to`;

          if (this.selectActionType === 'All') {
            text = text.replace('{{COUNT}}', `${this.totalRecord}`);
          } else {
            text = text.replace('{{COUNT}}', `${this.selectedItems.length}`);
          }

          this.dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '450px',
            data: {
              yesTitle: 'Ok',
              noTitle: 'Cancel',
              header: 'Confirmation',
              text,
            },
          });
          this.dialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.sendBulkEmail(response);
            } else {
              this._emitRefresh.emit(true);
            }
          });
        }
        this._loaderService.stop();
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        // this._toastrService.error(
        //   err.message ? err.message : MessageConstant.unknownError
        // );
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(MessageConstant.unknownError, '');
        }
      }
    );
  }
  sendBulkEmail(response) {
    this.dialogRef = this.dialog.open(SendBulkEmailDialogComponent, {
      width: '400px',
      panelClass: 're-bulk-container',
      disableClose: true,
      data: {
        items: this.selectedItems,
        moduleId: this.moduleId,
        templates: response.data?.items,
        actionType: this.selectedValue,
        isFilter: this.isFilter,
        filterObj: this.filterObj,
        searchStr: this.searchStr,
        selectActionType: this.selectActionType,
        latestCampaign: this.latestCampaign,
        previousCampaign: response.data.emailCampaignData,
      },
    });

    this.dialogRef.afterClosed().subscribe((data) => {
      if (typeof data !== 'undefined' && data == true) {
        this._emitRefresh.emit(data);
      } else if (typeof data !== 'undefined' && data == 'campaign') {
        this._emitRefresh.emit('campaign');
      } else {
        this._emitRefresh.emit();
      }
    });
  }
  openBulkSMSModal() {
    const phoneLength = this.selectedItems.filter((x) => x.contactNumber);
    if (!phoneLength?.length) {
      this._toastrService.error(this.messageConstant.selectContactWithNumber);
      return;
    }

    let length = 0;
    if (this.selectActionType === 'All') {
      length = this.totalRecord;
    } else {
      length = this.selectedItems.length;
    }

    if (length > environment.buyers.bulkSMSLimit) {
      const limit = this.messageConstant.selectBulkContactWithLimit
        .replace('[[TYPE]]', 'SMS')
        .replace('[[LIMIT]]', `${environment.buyers.bulkSMSLimit}`);
      this._toastrService.error(limit);
      return;
    }

    this.getSmsCredits();
  }
  getSmsCredits() {
    this._loaderService.start();

    const data = this.selectedItems
      .map((x) => {
        if (x?.contactNumber) {
          let obj = {
            toNumber: x?.contactNumber,
            subModuleId: x?._id,
          };
          return obj;
        }
      })
      .filter((x) => x);

    let obj: any = {
      toSend: this.selectActionType === 'All' ? [] : data,
      filterData: { contactNumber: { value: 'yes', operator: 'is' } },
      isFilter: this.selectActionType !== 'All' ? false : true,
    };

    if (this.isFilter) {
      obj = {
        ...obj,
        filterData: { ...obj.filterData, ...this.filterObj?.filterData },
      };
    }
    if (this.searchStr) {
      obj['searchStr'] = this.searchStr;
    }

    delete obj['page'];
    delete obj['limit'];

    this._creditService.getBulkSMSCredits(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode === 200) {
          const { buyerCount, smsCreditLimit, vendorCount } = response.data;
          if (this.moduleName === 'Vendors') {
            if (smsCreditLimit < vendorCount) {
              this._toastrService.error(this.messageConstant.smsCreditLimit);
              this._loaderService.stop();
              return;
            }
          } else {
            if (smsCreditLimit < buyerCount) {
              this._toastrService.error(this.messageConstant.smsCreditLimit);
              this._loaderService.stop();
              return;
            }
          }

          this.getNumbers();
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  getNumbers() {
    const data = {
      page: 1,
      limit: 1000,
      numTypeId: this.moduleName === 'Vendors' ? 4 : 2,
    };

    this._loaderService.start();
    this._sharedService.getnumbers(data).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode === 200) {
          // const tollFreeNumbers = response.data?.items?.filter(
          //   (x) => x.plivoSubType === 'tollfree'
          // );
          const tollFreeNumbers = response?.data?.items;

          if (tollFreeNumbers?.length) {
            let text = `You have selected {{COUNT}} records to send Bulk SMS to`;

            if (this.selectActionType === 'All') {
              text = text.replace('{{COUNT}}', `${this.totalRecord}`);
            } else {
              text = text.replace('{{COUNT}}', `${this.selectedItems.length}`);
            }

            this.dialogRef = this.dialog.open(ConfirmationDialogComponent, {
              width: '450px',
              data: {
                yesTitle: 'Ok',
                noTitle: 'Cancel',
                header: 'Confirmation',
                text,
              },
            });
            this.dialogRef.afterClosed().subscribe((result) => {
              if (result) {
                this.sendBulkSMS(tollFreeNumbers);
              } else {
                this._emitRefresh.emit(true);
              }
            });
          } else {
            this._toastrService.info(
              this.messageConstant.noTollFreeNumber.replace(
                /Buyers/g,
                this.moduleName
              ),
              '',
              {
                enableHtml: true,
              }
            );
          }
        }
        this._loaderService.stop();
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        this._toastrService.error(
          err.message ? err.message : MessageConstant.unknownError
        );
      }
    );
  }
  sendBulkSMS(tollFreeNumbers) {
    this.dialogRef = this.dialog.open(SendBulkSmsDialogComponent, {
      width: '500px',
      data: {
        items: this.selectedItems,
        tollFreeNumbers,
        moduleId: this.moduleId,
        actionType: this.selectedValue,
        isFilter: this.isFilter,
        filterObj: this.filterObj,
        searchStr: this.searchStr,
        selectActionType: this.selectActionType,
      },
    });

    this.dialogRef.afterClosed().subscribe((data) => {
      if (data) {
        this._emitRefresh.emit('sms');
      } else {
        this._emitRefresh.emit(true);
      }
    });
  }

  setView(type, val?) {
    const currentView = this.view.filter((x) => x.isActive);
    if (currentView?.length && currentView[0]?.name === type) {
      return;
    }

    const index = this.view.findIndex((x) => x.name == type);

    this.isList = type == 'LIST' ? true : false;

    if (index > -1) {
      this.view.map((x) => (x.isActive = false));
      this.view[index].isActive = true;
      if (val) this._emitView.emit(type);
    }
  }

  setSorting(type, val?) {
    this.sort.forEach((item) => {
      item.isActive = !item.isActive;
    });
    const index = this.sort.findIndex((x) => x?.isActive == true);
    if (val) this._emitSort.emit(this.sort[index].name);
  }

  selectListType(type: any) {
    if (type == 'all') {
      this.selectedListType = 'Show All';
    }
    if (type == 'monthly') {
      this.selectedListType = 'Month';
    }
    this._emitWhiteBoardListGroup.emit(type);
  }

  explainerVideos() {
    this._explainersPopUpService.explainerVideos(this.moduleName);
  }

  explainerVideosHide() {
    this._explainersPopUpService.explainerVideosHide(this.moduleName);
  }

  addEditGoal() {
    const currentUrl = this._router.url;

    this._router.navigate(['/kpis/manage-kpi-goals'], {
      state: {
        returnUrl: currentUrl,
        key: true,
      },
    });
  }

  filteredColumns() {
    return this.columns.filter((col) => {
      const matchesSearch = col.label
        .toLowerCase()
        .includes(this.searchText.toLowerCase());
      if (col.label === 'Dead Lead Reason') {
        return this.moduleName?.toLowerCase() === 'dead leads' && matchesSearch;
      }

      // Hide Estimated ARV and Asking Price for dead leads
      if (col.label === 'Estimated ARV' || col.label === 'Asking Price') {
        return this.moduleName?.toLowerCase() !== 'dead leads' && matchesSearch;
      }

      return matchesSearch;
    });
  }

  get filteredCols() {
    return this.columns.filter((col) => {
      const matchesSearch = col.label
        .toLowerCase()
        .includes(this.searchText.toLowerCase());

      if (col.label === 'Dead Lead Reason') {
        return this.moduleName?.toLowerCase() === 'dead leads' && matchesSearch;
      }

      // Hide Estimated ARV and Asking Price for dead leads
      if (col.label === 'Estimated ARV' || col.label === 'Asking Price') {
        return this.moduleName?.toLowerCase() !== 'dead leads' && matchesSearch;
      }

      // Hide Action column
      if (col.label === 'Action') {
        return false;
      }

      return matchesSearch;
    });
  }

  drop(event: CdkDragDrop<string[]>) {
    try {
      // Ensure the panel stays open during drag-drop operations
      if (!this.showColumnPanel) {
        this.showColumnPanel = true;
        this.cdr.detectChanges();
      }

      const fixedColumn = ['Seller Name', 'Property Address', 'Status'];
      const actionColumn = 'Action';
      const filteredCols = this.filteredCols;

      // Check if event indices are valid
      if (
        event.previousIndex < 0 ||
        event.previousIndex >= filteredCols.length
      ) {
        console.error(
          'Invalid previousIndex:',
          event.previousIndex,
          'filteredCols length:',
          filteredCols.length
        );
        return;
      }

      const draggedColumn = filteredCols[event.previousIndex];

      // Prevent moving Seller Name or replacing it as first column
      if (
        (event.previousIndex < 3 &&
          fixedColumn.includes(draggedColumn.label)) ||
        event.currentIndex < 3
      ) {
        this._toastrService.info(
          `The column  is fixed and cannot be rearranged.`
        );
        return;
      }

      // Prevent moving Action column
      if (draggedColumn.label === actionColumn) {
        this._toastrService.info(
          `The "${actionColumn}" column is always placed at the end automatically.`
        );
        return;
      }

      // Move column in filtered list
      const filteredColsCopy = JSON.parse(JSON.stringify(filteredCols));
      moveItemInArray(
        filteredColsCopy,
        event.previousIndex,
        event.currentIndex
      );

      // Create position map
      const positions = new Map();
      filteredColsCopy.forEach((col, i) => positions.set(col.label, i));

      // Update columns with new positions
      this.columns = this.columns
        .map((col) => ({
          ...col,
          sort: positions.has(col.label) ? positions.get(col.label) : col.sort,
        }))
        .sort((a, b) => {
          // Always keep Action column at the end
          if (a.label === actionColumn) return 1;
          if (b.label === actionColumn) return -1;

          // Sort other columns by position
          if (positions.has(a.label) && positions.has(b.label))
            return a.sort - b.sort;
          if (positions.has(a.label)) return -1;
          if (positions.has(b.label)) return 1;
          return a.sort - b.sort;
        })
        .map((col, i) => ({ ...col, sort: i }));

      // Force change detection to update the UI
      this.cdr.detectChanges();

      // Prevent the panel from closing after drag operation
      setTimeout(() => {
        if (!this.showColumnPanel) {
          this.showColumnPanel = true;
          this.cdr.detectChanges();
        }
      }, 0);
    } catch (error) {
      console.error('Error in drop method:', error);
    }
  }

  // Lead type column metadata
  private leadTypeColumns = {
    'Active Leads': {
      id: 'active-leads',
      type: 'active',
      configKey: 'leadsId',
      statusId: '5faa63fdfd20cd581703d255',
    },
    'Warm Leads': {
      id: 'warm-leads',
      type: 'warm',
      configKey: 'warmLeadId',
      statusId: '5ff82e5cd5b12b1502f6c5ae',
    },
    'Dead Leads': {
      id: 'dead-leads',
      type: 'dead',
      configKey: 'deadLeadId',
      statusId: '5ff82e5cd5b12b1502f6c5af',
    },
    'Referred To Agent': {
      id: 'referred-to-agent',
      type: 'referredtoagent',
      configKey: 'referredToAgentId',
      statusId: '61ea5943c53bc8037490af45',
    },
  };

  // Get column metadata if available
  private getColumnMetadata(label: string) {
    return this.leadTypeColumns[label] || null;
  }

  applyChanges(e?: MouseEvent) {
    let columnConfig = this.columns.map((col) => {
      const metadata = this.getColumnMetadata(col.label);
      const config = {
        label: col.label,
        value:
          col.value || col.key || col.label?.toLowerCase().replace(/\s+/g, '_'),
        sort: col.sort,
        isVisible: col.isVisible,
        key: col.key || null,
        ...(metadata && metadata),
      };
      if (col.hasRoles) {
        config.hasRoles = col.hasRoles;
        if (col.roles) {
          config.roles = JSON.parse(JSON.stringify(col.roles));
        }
      }
      return config;
    });

    columnConfig.sort((a, b) => a.sort - b.sort);
    this.originalColumns = JSON.parse(JSON.stringify(this.columns));
    this._emitColumnConfig.emit(columnConfig);
    this.showColumnPanel = false;
    if (e) e.stopPropagation();
  }

  cancelChanges(e?: MouseEvent) {
    // Reset columns and hide panel
    this.columns = JSON.parse(JSON.stringify(this.originalColumns));
    const teamAssignedColumn = this.columns.find(
      (col) => col.label === 'Team Assigned'
    );
    if (teamAssignedColumn && teamAssignedColumn.roles) {
      this.roleOptions = teamAssignedColumn.roles.map((role) => ({
        key: role.roleId,
        label: role.roleName,
        isVisible: role.isVisible,
      }));
      this.selectedRoles = teamAssignedColumn.roles
        .filter((role) => role.isVisible)
        .map((role) => role.roleId);
    }

    this.showColumnPanel = false;
    if (e) e.stopPropagation();
  }
  onColumnVisibilityChange(column: any) {
    // Toggle column visibility
    const col = this.columns.find((c) => c.label === column.label);
    if (col) col.isVisible = !col.isVisible;
  }

  // Get role options for Team Assigned column
  getRoleOptions() {
    // Return the pre-computed role options for better performance
    return this.roleOptions;
  }

  // Check if a role is visible
  isRoleSelected(roleKey: string) {
    // Use the pre-computed role options for better performance
    const roleOption = this.roleOptions.find(
      (option) => option.key === roleKey
    );
    return roleOption ? roleOption.isVisible : false;
  }

  // Toggle role visibility
  toggleRoleSelection(roleKey: string) {
    const teamAssignedColumn = this.columns.find((col) => col.hasRoles);
    if (!teamAssignedColumn || !teamAssignedColumn.roles) return;

    const role = teamAssignedColumn.roles.find((r) => r.roleId === roleKey);
    if (role) {
      role.isVisible = !role.isVisible;

      // Also update the corresponding role in roleOptions
      const roleOption = this.roleOptions.find(
        (option) => option.key === roleKey
      );
      if (roleOption) {
        roleOption.isVisible = role.isVisible;
      }
    }

    this.selectedRoles = teamAssignedColumn.roles
      .filter((r) => r.isVisible)
      .map((r) => r.roleId);
  }

  // Handle drag start event
  onDragStarted(): void {
    // Ensure the panel stays open during drag
    document.body.classList.add('dragging-active');
    this.showColumnPanel = true;
    this.cdr.detectChanges();
  }

  // Handle drag end event
  onDragEnded(): void {
    // Small delay to ensure the drop event completes
    setTimeout(() => {
      document.body.classList.remove('dragging-active');
    }, 100);
  }

  addAction() {
    this._addGlobalFolder.emit();
  }
}
